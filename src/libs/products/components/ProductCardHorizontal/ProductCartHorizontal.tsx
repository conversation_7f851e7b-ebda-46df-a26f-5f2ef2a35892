import type { ReactNode } from 'react';
import { ProductType } from '@/types';
import { ProductImage } from '../ProductImage/ProductImage';

interface ProductCartHorizontalProps {
  productOfferId: string;
  product: Partial<ProductType> & {
    id: ProductType['id'];
    name: ProductType['name'];
    imageUrl: ProductType['imageUrl'];
  };
  content: ReactNode;
  actions?: ReactNode;
}

export const ProductCartHorizontal = ({
  productOfferId,
  product,
  content,
  actions,
}: ProductCartHorizontalProps) => {
  return (
    <div className="relative flex w-full p-4 px-0">
      <ProductImage productOfferId={productOfferId} product={product} />
      <div className="flex w-full gap-5">
        <div className="flex-1">{content}</div>
        {actions && <div className="w-36">{actions}</div>}
      </div>
    </div>
  );
};
