import type { CartItemType } from '@/libs/cart/types';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';
import { ProductActions } from './components/ProductActions/ProductActions';
import { ProductInfo } from './components/ProductInfo/ProductInfo';

type CartVendorProductItemProps = {
  data: CartItemType;
};
export const CartVendorProductItem = ({ data }: CartVendorProductItemProps) => {
  return (
    <div className="relative flex w-full pb-4">
      <ProductImage data={data} />
      <ProductInfo data={data} />
      <ProductActions data={data} />
    </div>
  );
};
