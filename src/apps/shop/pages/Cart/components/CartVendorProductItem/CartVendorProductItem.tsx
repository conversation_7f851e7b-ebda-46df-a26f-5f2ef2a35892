import type { CartItemType } from '@/libs/cart/types';
import { ProductImage } from '@/libs/products/components/ProductImage/ProductImage';
import { ProductActions } from './components/ProductActions/ProductActions';
import { ProductInfo } from './components/ProductInfo/ProductInfo';

type CartVendorProductItemProps = {
  data: CartItemType;
};
export const CartVendorProductItem = ({ data }: CartVendorProductItemProps) => {
  return (
    <div className="relative flex w-full p-4 px-0">
      <ProductImage data={data} />
      <div className="flex w-full gap-5">
        <ProductInfo data={data} />
        <ProductActions data={data} />
      </div>
    </div>
  );
};
